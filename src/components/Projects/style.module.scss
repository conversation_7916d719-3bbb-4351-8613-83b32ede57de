.projects {
  background: #FCFCF8;
    display: flex;
    align-items: center;
    // padding-left: 200px;
    // padding-right: 200px;
    flex-direction: column;
    margin-top: 50px;
    padding-bottom: var(--section-padding);
  
    .body {
        // version desktop => align-items center, etc.
        display: flex;
        width: 100%;
        flex-direction: column;
      
        @media (max-width: 1200px) {
          // en dessous de 1200px => 2 colonnes (grid)
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 30px; // espace horizontal/vertical entre les “card”
        }
      
        @media (max-width: 768px) {
          // en dessous de 768px => 1 colonne
          grid-template-columns: 1fr;
        }
      }
      
  
    .modalContainer {
      height: 350px;
      width: 400px;
      position: fixed;
      top: 0;
      left: 0;
      background-color: white;
      pointer-events: none;
      overflow: hidden;
      z-index: 998;
      transform: translate(-50%, -50%);
      will-change: left, top;
    }
  
    .modalSlider {
      height: 100%;
      width: 100%;
      position: relative;
      transition: top 0.5s cubic-bezier(0.76, 0, 0.24, 1);
    }
  
    .modal {
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  
    .modal img {
      height: auto;
    }
  

  }
  
  /* ---- le header en colonnes (client, location, services, year) ---- */
  .headerRow {
    /* pour aligner les labels */
    display: flex;
    width: 100%;
    padding-bottom: 40px;
    box-sizing: border-box;
  
    /* on peut reprendre tes largeurs/paddings style-x si tu veux */
    .colClient {
      width: 65%;
      padding-left: 69px;
    }
    .colLocation {
      width: 25%;
    }
    .colServices {
      width: 15%;
    }
    .colYear {
      width: 5%;
      text-align: center;
    }
  
    h5 {
      /* style du label */
      margin: 0;
      font-size: 9.6px;
      text-transform: uppercase;
      letter-spacing: 0.48px;
      opacity: 0.5;
      font-family: 'Gregg Sans', sans-serif;
      font-style: normal;
      font-weight: 450;
      line-height: 10.224px;
    }
  }
  
  /* ---- style pour chaque projet ---- */
  .project {
    /* ton style existant ou un minimum d'espace entre projets */
    margin-bottom: 20px; 
    width: 100%;
        border: none; // plus de séparateur
        // etc.
        .cols { display: none; } // caché sur mobile
        .gridLayout { display: block; } // la version “carte”
    
      
    
    /* conteneur interne qui adopte la même grille (44% / 20% / 22% / 14%) */
    .cols {
      display: flex;
      align-items: center;
      width: 100%;
      box-sizing: border-box;
  
      .colClient {
        width: 44%;
        padding-left: 69px;
      }
      .colLocation {
        width: 25%;
      }
      .colServices {
        width: 15%;
      }
      .colYear {
        width: 5%;
        text-align: center;
      }
    }
  }
  




//   ---------------




@media (min-width: 1200px) {

    .projects {
        margin-top: 100px;
    }

}

  @media (max-width: 1200px) {

.singleProject {
    // padding-bottom: calc(var(--section-padding)* 1);
    padding-top: var(--gap-padding);
    padding-bottom: var(--gap-padding);
}

    .headerRow {
        display: none;
    }

    .project {
      // border: none; // plus de séparateur
              // etc.
              .cols { display: none; } // caché sur mobile
              .gridLayout { display: block; } // la version “carte”
            }

        }
        


        // Éviter les flash visuels
        .fadeMe {
            opacity: 0;
            transform: translateY(50px); // Position initiale
            will-change: opacity, transform; // Optimisation du rendu
          }
          








/* Styles pour les vues liste et grille sur desktop - Approche professionnelle */
@media (min-width: 1200px) {
  /* Vue liste (par défaut) */
  .body.listView {
    display: flex;
    flex-direction: column;
  }

  /* Vue grille (comme mobile) */
  .body.gridView {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: calc(var(--gap-padding) * 2);
  }
}
